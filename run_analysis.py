#!/usr/bin/env python3
"""
Simple runner script for Discord LLM Analysis

This script provides a simple interface to run the Discord server analysis.
"""

import asyncio
import sys
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

def check_requirements():
    """Check if all requirements are met"""
    errors = []
    
    # Check if database exists
    if not Path("database/cfvrc.sqlite").exists():
        errors.append("Database file 'database/cfvrc.sqlite' not found")
    
    # Check if config is properly set
    try:
        from config import BOT_TOKEN
        if not BOT_TOKEN or BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
            errors.append("Please set your Discord bot token in config.py")
    except ImportError:
        errors.append("config.py file not found")
    
    # Check if required packages are installed
    try:
        import aiohttp
        import requests
    except ImportError as e:
        errors.append(f"Missing required package: {e.name}")
        errors.append("Run: pip install -r requirements_analyzer.txt")
    
    return errors

def print_usage():
    """Print usage instructions"""
    print("""
Discord Server LLM Analyzer
===========================

This script analyzes Discord messages from your archived database using a local Ollama LLM.

Prerequisites:
1. Make sure Ollama is running locally with the deepseek-r1:8b model
   - Install Ollama: https://ollama.ai/
   - Run: ollama pull deepseek-r1:8b
   - Start Ollama service

2. Set your Discord bot token in config.py
   - Get a bot token from https://discord.com/developers/applications
   - Replace "YOUR_BOT_TOKEN_HERE" in config.py with your actual token

3. Install required Python packages:
   pip install -r requirements_analyzer.txt

Usage:
   python run_analysis.py

The script will:
- Create a user lookup table using the Discord API
- Group messages by day and channel
- Send them to your local Ollama LLM for analysis
- Save analysis reports in the 'analysis_reports' directory

Note: This process may take a while depending on the amount of data.
""")

async def main():
    """Main function"""
    print("Discord Server LLM Analyzer")
    print("=" * 30)
    
    # Check requirements
    errors = check_requirements()
    if errors:
        print("❌ Requirements check failed:")
        for error in errors:
            print(f"   - {error}")
        print("\nPlease fix these issues before running the analysis.")
        print_usage()
        return 1
    
    print("✅ All requirements met!")
    
    # Ask for confirmation
    print("\nThis will analyze all messages in your database and may take a while.")
    response = input("Do you want to continue? (y/N): ").strip().lower()
    
    if response not in ['y', 'yes']:
        print("Analysis cancelled.")
        return 0
    
    # Run the analysis
    try:
        from llm_analyzer import DiscordLLMAnalyzer
        analyzer = DiscordLLMAnalyzer()
        await analyzer.run_analysis()
        
        print("\n✅ Analysis complete!")
        print(f"Check the 'analysis_reports' directory for results.")
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\nAnalysis interrupted by user.")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
