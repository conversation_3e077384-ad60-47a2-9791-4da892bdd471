#!/usr/bin/env python3
"""
Simple runner script for Discord LLM Analysis

This script provides a simple interface to run the Discord server analysis.
"""

import asyncio
import sys
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

def check_requirements():
    """Check if all requirements are met"""
    errors = []

    # Check if database exists
    if not Path("database/cfvrc.sqlite").exists():
        errors.append("Database file 'database/cfvrc.sqlite' not found")

    # Check if config is properly set
    try:
        from config import BOT_TOKEN
        if not BOT_TOKEN or BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
            errors.append("Please set your Discord bot token in config.py")
    except ImportError:
        errors.append("config.py file not found")

    # Check if required packages are installed
    try:
        import aiohttp
        import requests
    except ImportError as e:
        errors.append(f"Missing required package: {e.name}")
        errors.append("Run: pip install -r requirements_analyzer.txt")

    return errors

def show_progress_info():
    """Show current progress information"""
    progress_file = Path("analysis_progress.json")
    if not progress_file.exists():
        return

    try:
        import json
        with open(progress_file, 'r', encoding='utf-8') as f:
            progress = json.load(f)

        completed = len(progress.get('completed_days', []))
        total = progress.get('total_days', 0)
        failed = len(progress.get('failed_days', []))

        if total > 0:
            print(f"\n📊 Current Progress:")
            print(f"   Completed: {completed}/{total} days ({completed/total*100:.1f}%)")
            if failed > 0:
                print(f"   Failed: {failed} days")
            print(f"   You can resume from where you left off.")
    except Exception:
        pass

def print_usage():
    """Print usage instructions"""
    print("""
Discord Server LLM Analyzer
===========================

This script analyzes Discord messages from your archived database using a local Ollama LLM.

Prerequisites:
1. Make sure Ollama is running locally with the deepseek-r1:8b model
   - Install Ollama: https://ollama.ai/
   - Run: ollama pull deepseek-r1:8b
   - Start Ollama service

2. Set your Discord bot token in config.py
   - Get a bot token from https://discord.com/developers/applications
   - Replace "YOUR_BOT_TOKEN_HERE" in config.py with your actual token

3. Install required Python packages:
   pip install -r requirements_analyzer.txt

Usage:
   python run_analysis.py

The script will:
- Create a user lookup table using the Discord API
- Group messages by day and channel
- Send them to your local Ollama LLM for analysis
- Save analysis reports in the 'analysis_reports' directory

Rate Limiting Issues:
If you encounter Discord API rate limiting (HTTP 429 errors):
   python create_user_lookup.py    # Run user lookup separately first
   python run_analysis.py          # Then run the main analysis

Pause/Resume Features:
- Press Ctrl+C to pause the analysis at any time
- Progress is automatically saved
- Run the script again to resume from where you left off
- Use 'python manage_progress.py status' to check progress
- Use 'python manage_progress.py reset' to start over

Note: This process may take several hours depending on the amount of data.
""")

async def main():
    """Main function"""
    print("Discord Server LLM Analyzer")
    print("=" * 30)
    
    # Check requirements
    errors = check_requirements()
    if errors:
        print("❌ Requirements check failed:")
        for error in errors:
            print(f"   - {error}")
        print("\nPlease fix these issues before running the analysis.")
        print_usage()
        return 1
    
    print("✅ All requirements met!")

    # Show progress info if available
    show_progress_info()

    # Ask for confirmation
    print("\nThis will analyze all messages in your database and may take a while.")
    print("💡 You can pause anytime with Ctrl+C and resume later.")
    response = input("Do you want to continue? (y/N): ").strip().lower()
    
    if response not in ['y', 'yes']:
        print("Analysis cancelled.")
        return 0
    
    # Run the analysis
    try:
        from llm_analyzer import DiscordLLMAnalyzer
        analyzer = DiscordLLMAnalyzer()
        await analyzer.run_analysis()
        
        print("\n✅ Analysis complete!")
        print(f"Check the 'analysis_reports' directory for results.")
        print(f"💡 Use 'python manage_progress.py status' to see final statistics.")

    except KeyboardInterrupt:
        print("\n\n⏸️  Analysis paused by user.")
        print("📊 Progress has been saved automatically.")
        print("🔄 To resume: run 'python run_analysis.py' again")
        print("📈 To check progress: run 'python manage_progress.py status'")
        return 0
    except Exception as e:
        error_msg = str(e).lower()
        if "429" in error_msg or "rate limit" in error_msg:
            logger.error("Analysis failed due to Discord API rate limiting.")
            print("\n🚫 Rate Limiting Issue Detected!")
            print("💡 Try running the user lookup separately first:")
            print("   python create_user_lookup.py")
            print("   python run_analysis.py")
        else:
            logger.error(f"Analysis failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\nAnalysis interrupted by user.")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
