"""
Configuration file for Discord LLM Analyzer
"""

# Database configuration
DATABASE_PATH = "database/cfvrc.sqlite"

# Discord Bot Token (replace with your actual token if different)
# You can get this from https://discord.com/developers/applications
# This is the token from the existing bot.py file
BOT_TOKEN = "MTQwNzA3NDczNzI2MzM0NTY4NQ.G9DCZS.SewjrZNQHoeCUv5OvFRP8cOlXu6HCxiJSMhUXM"

# Ollama configuration
OLLAMA_URL = "http://localhost:11434"
MODEL_NAME = "deepseek-r1:8b"

# Analysis configuration
MAX_CONTEXT_TOKENS = 128000  # 128k context window
OUTPUT_DIR = "analysis_reports"

# Rate limiting (seconds between requests)
DISCORD_API_DELAY = 0.02  # 50 requests per second max
OLLAMA_DELAY = 1.0  # Delay between Ollama requests
DAY_ANALYSIS_DELAY = 2.0  # Delay between analyzing different days
