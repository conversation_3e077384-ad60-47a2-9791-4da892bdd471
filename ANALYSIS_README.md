# Discord Server LLM Analysis Tool

This tool analyzes Discord messages from your archived database using a local Ollama LLM (deepseek-r1:8b). It creates detailed daily analysis reports focusing on server dynamics, conflicts, and mental health concerns.

## Features

- **User Lookup**: Automatically fetches Discord usernames using the Discord API
- **Daily Analysis**: Groups messages by day and channel for comprehensive analysis
- **Context Management**: Handles the 128k context window by splitting large days into multiple chunks
- **Focused Analysis**: Specifically looks for interactions involving key users and stress-causing events
- **Automated Reports**: Saves analysis as dated text files for later review
- **Pause/Resume**: Can be paused with Ctrl+C and resumed later - progress is automatically saved
- **Progress Tracking**: Built-in progress management with detailed status reporting

## Prerequisites

### 1. Ollama Setup
```bash
# Install Ollama (visit https://ollama.ai/ for installation instructions)
# Then pull the required model:
ollama pull deepseek-r1:8b

# Make sure Ollama is running (usually starts automatically)
# You can test it with: curl http://localhost:11434/api/tags
```

### 2. Discord Bot Token
1. Go to https://discord.com/developers/applications
2. Create a new application or use an existing one
3. Go to the "Bot" section
4. Copy the bot token
5. Edit `config.py` and replace `"YOUR_BOT_TOKEN_HERE"` with your actual token

### 3. Python Dependencies
```bash
pip install -r requirements_analyzer.txt
```

## Configuration

Edit `config.py` to customize the analysis:

```python
# Database path
DATABASE_PATH = "database/cfvrc.sqlite"

# Your Discord bot token
BOT_TOKEN = "your_actual_bot_token_here"

# Ollama settings
OLLAMA_URL = "http://localhost:11434"
MODEL_NAME = "deepseek-r1:8b"

# Analysis settings
MAX_CONTEXT_TOKENS = 128000  # 128k context window
OUTPUT_DIR = "analysis_reports"
```

## Usage

### Simple Usage
```bash
# Start or resume analysis
python run_analysis.py

# Check progress
python manage_progress.py status

# Reset progress (start over)
python manage_progress.py reset
```

### Progress Management
```bash
# Show detailed progress information
python manage_progress.py status

# Reset all progress and start over
python manage_progress.py reset

# Reset only failed days for retry
python manage_progress.py reset-failed

# Remove a specific day from completed list
python manage_progress.py remove-day
```

### Advanced Usage
```python
import asyncio
from llm_analyzer import DiscordLLMAnalyzer

async def main():
    analyzer = DiscordLLMAnalyzer()
    await analyzer.run_analysis()

asyncio.run(main())
```

## How It Works

1. **User Lookup Creation**: The script fetches all unique user IDs from the database and uses the Discord API to get their usernames and display names.

2. **Message Grouping**: Messages are grouped by date and then by channel within each date.

3. **Context Management**: If a day's messages exceed the 128k token limit, they're split into multiple chunks by channel.

4. **LLM Analysis**: Each day (or chunk) is sent to the local Ollama LLM with a specific prompt focusing on:
   - Key participants and their interactions
   - Conflicts and disagreements
   - Emotional tone and mental health concerns
   - Specific attention to users "Guardian" and "YourLocalCowboy"
   - Server management issues

5. **Report Generation**: Analysis results are saved as text files named `analysis_YYYY-MM-DD.txt` in the `analysis_reports` directory.

## Pause/Resume Functionality

The analysis system includes robust pause/resume capabilities:

### How to Pause
- Press **Ctrl+C** at any time during analysis
- Progress is automatically saved to `analysis_progress.json`
- The system will gracefully stop after completing the current day

### How to Resume
- Simply run `python run_analysis.py` again
- The system automatically detects previous progress
- Analysis continues from where it left off
- Already completed days are skipped

### Progress Tracking
- **User Lookup**: Cached after first completion, won't be repeated
- **Daily Analysis**: Each day is marked as completed after successful analysis
- **Failed Days**: Days that encounter errors are tracked separately
- **Real-time Updates**: Progress file is updated after each day

### Progress Management Commands
```bash
# Check current status
python manage_progress.py status

# Reset everything and start over
python manage_progress.py reset

# Retry only failed days
python manage_progress.py reset-failed

# Remove a specific completed day to re-analyze it
python manage_progress.py remove-day
```

## Output Format

Each analysis report includes:
- Date and summary of participants
- Key events and disagreements
- Emotional tone assessment
- Specific focus on problematic interactions
- Recommendations for server management

## File Structure

```
DiscordServerArchiver/
├── llm_analyzer.py          # Main analysis script
├── run_analysis.py          # Simple runner script
├── manage_progress.py       # Progress management script
├── test_setup.py           # Setup verification script
├── config.py               # Configuration file
├── requirements_analyzer.txt # Python dependencies
├── analysis_progress.json  # Progress tracking (auto-generated)
├── analysis_reports/       # Output directory
│   ├── user_lookup.json   # Cached user information
│   ├── analysis_2023-03-01.txt
│   ├── analysis_2023-03-02.txt
│   └── ...
└── database/
    └── cfvrc.sqlite       # Your Discord message database
```

## Troubleshooting

### Common Issues

1. **"Ollama not responding"**
   - Make sure Ollama is running: `ollama serve`
   - Check if the model is available: `ollama list`
   - Verify the URL in config.py

2. **"Discord API rate limit"**
   - The script includes rate limiting, but if you hit limits, wait and try again
   - Consider running the analysis in smaller batches

3. **"Context window exceeded"**
   - The script automatically splits large days, but very active days might still be too large
   - Consider adjusting `MAX_CONTEXT_TOKENS` in config.py

4. **"Bot token invalid"**
   - Make sure your bot token is correct in config.py
   - Ensure the bot has necessary permissions (read message history)

### Performance Tips

- The analysis can take several hours for large servers
- Consider running overnight for comprehensive analysis
- Monitor your system resources as the LLM can be CPU/memory intensive
- The script saves progress, so you can interrupt and resume

## Privacy and Security

- User lookup data is cached locally in `user_lookup.json`
- No data is sent to external services except Discord API for username resolution
- All analysis is performed locally using your Ollama instance
- Consider the sensitivity of the chat logs when sharing analysis reports

## Customization

You can modify the analysis prompt in the `format_messages_for_llm` method to focus on different aspects or change the analysis style. The current prompt is specifically designed for analyzing server management issues and mental health concerns in a Christian furry community context.
