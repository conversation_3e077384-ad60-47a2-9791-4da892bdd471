import sqlite3
from pathlib import Path
import requests
import json
import os
from urllib.parse import urlparse
import time

def download_image(url, filepath):
    """Download an image from URL to the specified filepath."""
    try:
        # Add a small delay to be respectful to <PERSON>rd's servers
        time.sleep(0.1)

        response = requests.get(url, timeout=30)
        response.raise_for_status()

        # Create directory if it doesn't exist
        filepath.parent.mkdir(parents=True, exist_ok=True)

        # Write the image data to file
        with open(filepath, 'wb') as f:
            f.write(response.content)

        print(f"Downloaded: {filepath.name}")
        return True

    except requests.exceptions.RequestException as e:
        print(f"Failed to download {url}: {e}")
        return False
    except Exception as e:
        print(f"Error saving {filepath}: {e}")
        return False

def get_filename_from_url(url):
    """Extract filename from URL, handling Discord CDN URLs."""
    parsed_url = urlparse(url)
    filename = os.path.basename(parsed_url.path)

    # If no filename found, create one based on the URL
    if not filename or '.' not in filename:
        filename = f"attachment_{hash(url) % 100000}.jpg"

    return filename

def get_unique_filepath(base_filepath):
    """Generate a unique filepath by adding a counter if the file already exists."""
    if not base_filepath.exists():
        return base_filepath

    # Split filename and extension
    stem = base_filepath.stem
    suffix = base_filepath.suffix
    parent = base_filepath.parent

    counter = 1
    while True:
        new_filename = f"{stem}_{counter}{suffix}"
        new_filepath = parent / new_filename
        if not new_filepath.exists():
            return new_filepath
        counter += 1

if __name__ == "__main__":
    # Connect to the database
    connection = sqlite3.connect('database/cfvrc.sqlite')
    cursor = connection.cursor()

    # Query messages with attachments (where attachment_url_list is not empty)
    print("Querying database for messages with attachments...")
    query = "SELECT channel_id, attachment_url_list FROM messages WHERE attachment_url_list != '[]'"
    cursor.execute(query)
    rows = cursor.fetchall()

    print(f"Found {len(rows)} messages with attachments")

    # Collect all URLs organized by channel
    channel_urls = {}
    total_urls = 0

    for channel_id, attachment_url_list_json in rows:
        try:
            # Parse the JSON string to get the list of URLs
            url_list = json.loads(attachment_url_list_json)

            if channel_id not in channel_urls:
                channel_urls[channel_id] = []

            channel_urls[channel_id].extend(url_list)
            total_urls += len(url_list)

        except json.JSONDecodeError as e:
            print(f"Error parsing JSON for channel {channel_id}: {e}")
            continue

    print(f"Total URLs to download: {total_urls}")
    print(f"Channels with attachments: {len(channel_urls)}")

    # Create base images directory
    base_images_dir = Path("images")
    base_images_dir.mkdir(exist_ok=True)

    # Download images for each channel
    downloaded_count = 0
    failed_count = 0

    for channel_id, urls in channel_urls.items():
        print(f"\nProcessing channel {channel_id} ({len(urls)} images)...")

        # Create channel-specific directory
        channel_dir = base_images_dir / f"channel_{channel_id}"

        for i, url in enumerate(urls, 1):
            filename = get_filename_from_url(url)
            base_filepath = channel_dir / filename

            # Get a unique filepath (adds counter if file exists)
            filepath = get_unique_filepath(base_filepath)

            print(f"Downloading {i}/{len(urls)}: {filepath.name}")

            if download_image(url, filepath):
                downloaded_count += 1
            else:
                failed_count += 1

    # Close database connection
    connection.close()

    print(f"\n=== Download Summary ===")
    print(f"Total images downloaded: {downloaded_count}")
    print(f"Failed downloads: {failed_count}")
    print(f"Images saved to: {base_images_dir.absolute()}")