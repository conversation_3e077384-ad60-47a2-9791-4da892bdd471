#!/usr/bin/env python3
"""
Standalone script to create user lookup table with robust rate limiting

This script can be run separately to handle Discord API rate limits more gracefully.
It saves progress frequently and can be resumed if interrupted.
"""

import sqlite3
import json
import asyncio
import aiohttp
import logging
from pathlib import Path
from config import *

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

class UserLookupCreator:
    def __init__(self):
        self.database_path = DATABASE_PATH
        self.bot_token = BOT_TOKEN
        self.output_dir = Path(OUTPUT_DIR)
        self.output_dir.mkdir(exist_ok=True)
        self.user_lookup = {}
        self.failed_users = set()
        
    def load_existing_lookup(self):
        """Load existing user lookup if available"""
        lookup_file = self.output_dir / "user_lookup.json"
        if lookup_file.exists():
            try:
                with open(lookup_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # Convert string keys back to integers
                    self.user_lookup = {int(k): v for k, v in data.items()}
                logger.info(f"Loaded existing lookup for {len(self.user_lookup)} users")
            except Exception as e:
                logger.warning(f"Could not load existing lookup: {e}")
    
    def save_lookup(self):
        """Save current lookup to file"""
        try:
            with open(self.output_dir / "user_lookup.json", 'w', encoding='utf-8') as f:
                json.dump(self.user_lookup, f, indent=2, ensure_ascii=False)
            logger.debug(f"Saved lookup for {len(self.user_lookup)} users")
        except Exception as e:
            logger.error(f"Failed to save lookup: {e}")
    
    def get_users_to_fetch(self):
        """Get list of user IDs that need to be fetched"""
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        cursor.execute("SELECT DISTINCT author_id FROM messages")
        all_user_ids = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        # Filter out users we already have
        existing_users = set(self.user_lookup.keys())
        remaining_users = [uid for uid in all_user_ids if uid not in existing_users and uid not in self.failed_users]
        
        return all_user_ids, remaining_users
    
    async def fetch_user_batch(self, session, user_ids, start_idx, batch_size=10):
        """Fetch a batch of users with proper rate limiting"""
        batch = user_ids[start_idx:start_idx + batch_size]
        results = []
        
        for user_id in batch:
            success = False
            retries = 0
            
            while not success and retries < DISCORD_API_MAX_RETRIES:
                try:
                    async with session.get(f'https://discord.com/api/v10/users/{user_id}') as response:
                        if response.status == 200:
                            user_data = await response.json()
                            username = user_data.get('username', f'Unknown_{user_id}')
                            display_name = user_data.get('global_name', username)
                            self.user_lookup[user_id] = {
                                'username': username,
                                'display_name': display_name
                            }
                            results.append(f"✅ {username}")
                            success = True
                            
                        elif response.status == 429:
                            # Rate limited
                            retry_after = response.headers.get('Retry-After')
                            if retry_after:
                                wait_time = int(retry_after)
                            else:
                                wait_time = DISCORD_API_RETRY_DELAY
                            
                            logger.warning(f"Rate limited. Waiting {wait_time} seconds...")
                            await asyncio.sleep(wait_time)
                            retries += 1
                            
                        elif response.status == 404:
                            # User not found
                            self.user_lookup[user_id] = {
                                'username': f'DeletedUser_{user_id}',
                                'display_name': f'DeletedUser_{user_id}'
                            }
                            results.append(f"🗑️ DeletedUser_{user_id}")
                            success = True
                            
                        elif response.status == 403:
                            # Forbidden (bot doesn't have permission)
                            self.user_lookup[user_id] = {
                                'username': f'PrivateUser_{user_id}',
                                'display_name': f'PrivateUser_{user_id}'
                            }
                            results.append(f"🔒 PrivateUser_{user_id}")
                            success = True
                            
                        else:
                            logger.warning(f"HTTP {response.status} for user {user_id}")
                            retries += 1
                            if retries < DISCORD_API_MAX_RETRIES:
                                await asyncio.sleep(DISCORD_API_DELAY * retries)
                
                except Exception as e:
                    logger.error(f"Error fetching user {user_id}: {e}")
                    retries += 1
                    if retries < DISCORD_API_MAX_RETRIES:
                        await asyncio.sleep(DISCORD_API_DELAY * retries)
            
            if not success:
                self.failed_users.add(user_id)
                self.user_lookup[user_id] = {
                    'username': f'Unknown_{user_id}',
                    'display_name': f'Unknown_{user_id}'
                }
                results.append(f"❌ Unknown_{user_id}")
            
            # Rate limiting between individual requests
            await asyncio.sleep(DISCORD_API_DELAY)
        
        return results
    
    async def create_lookup(self):
        """Main function to create user lookup"""
        if not BOT_TOKEN or BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
            logger.error("Please set your Discord bot token in config.py")
            return False
        
        logger.info("Starting user lookup creation...")
        
        # Load existing data
        self.load_existing_lookup()
        
        # Get users to fetch
        all_users, remaining_users = self.get_users_to_fetch()
        
        if not remaining_users:
            logger.info("All users already in lookup table!")
            return True
        
        logger.info(f"Total users: {len(all_users)}")
        logger.info(f"Already have: {len(all_users) - len(remaining_users)}")
        logger.info(f"Need to fetch: {len(remaining_users)}")
        
        # Estimate time
        estimated_seconds = len(remaining_users) * DISCORD_API_DELAY
        estimated_minutes = estimated_seconds / 60
        logger.info(f"Estimated time: {estimated_minutes:.1f} minutes")
        
        headers = {
            'Authorization': f'Bot {BOT_TOKEN}',
            'Content-Type': 'application/json'
        }
        
        try:
            async with aiohttp.ClientSession(headers=headers) as session:
                batch_size = 10  # Process in small batches
                
                for i in range(0, len(remaining_users), batch_size):
                    try:
                        batch_results = await self.fetch_user_batch(session, remaining_users, i, batch_size)
                        
                        # Progress update
                        processed = min(i + batch_size, len(remaining_users))
                        percentage = (processed / len(remaining_users)) * 100
                        logger.info(f"Progress: {processed}/{len(remaining_users)} ({percentage:.1f}%)")
                        
                        # Show some results
                        for result in batch_results[:3]:  # Show first 3 results
                            logger.debug(f"  {result}")
                        if len(batch_results) > 3:
                            logger.debug(f"  ... and {len(batch_results) - 3} more")
                        
                        # Save progress every batch
                        self.save_lookup()
                        
                        # Longer pause between batches to be extra safe
                        if i + batch_size < len(remaining_users):
                            await asyncio.sleep(1.0)
                            
                    except KeyboardInterrupt:
                        logger.info("Interrupted by user. Saving progress...")
                        self.save_lookup()
                        raise
                    except Exception as e:
                        logger.error(f"Error processing batch starting at {i}: {e}")
                        # Save progress even on error
                        self.save_lookup()
                        # Wait a bit longer before continuing
                        await asyncio.sleep(5.0)
        
        except KeyboardInterrupt:
            logger.info("User lookup creation paused. Progress saved.")
            return False
        except Exception as e:
            logger.error(f"Fatal error in user lookup creation: {e}")
            return False
        
        # Final save
        self.save_lookup()
        
        logger.info(f"User lookup creation complete!")
        logger.info(f"Successfully fetched: {len(self.user_lookup)} users")
        if self.failed_users:
            logger.warning(f"Failed to fetch: {len(self.failed_users)} users")
        
        return True

async def main():
    creator = UserLookupCreator()
    success = await creator.create_lookup()
    return 0 if success else 1

if __name__ == "__main__":
    import sys
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\nUser lookup creation interrupted by user.")
        print("Progress has been saved. Run the script again to continue.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)
