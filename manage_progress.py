#!/usr/bin/env python3
"""
Progress management script for Discord LLM Analysis

This script allows you to check progress, reset analysis, or manage failed days.
"""

import json
import sys
from pathlib import Path
from datetime import datetime
import argparse

def load_progress():
    """Load progress data"""
    progress_file = Path("analysis_progress.json")
    if not progress_file.exists():
        return None
    
    try:
        with open(progress_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading progress: {e}")
        return None

def save_progress(data):
    """Save progress data"""
    try:
        with open("analysis_progress.json", 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"Error saving progress: {e}")
        return False

def show_status():
    """Show current analysis status"""
    progress = load_progress()
    
    if not progress:
        print("❌ No progress file found. Analysis hasn't been started yet.")
        return
    
    print("📊 Discord LLM Analysis Status")
    print("=" * 40)
    
    # Basic stats
    completed = len(progress.get('completed_days', []))
    failed = len(progress.get('failed_days', []))
    total = progress.get('total_days', 0)
    remaining = max(0, total - completed - failed)
    
    print(f"Total Days: {total}")
    print(f"Completed: {completed}")
    print(f"Failed: {failed}")
    print(f"Remaining: {remaining}")
    
    if total > 0:
        completion_pct = (completed / total) * 100
        print(f"Progress: {completion_pct:.1f}%")
    
    # Current status
    current_day = progress.get('current_day')
    if current_day:
        print(f"Current/Last Day: {current_day}")
    
    last_updated = progress.get('last_updated')
    if last_updated:
        print(f"Last Updated: {last_updated}")
    
    # User lookup status
    user_lookup_complete = progress.get('user_lookup_complete', False)
    print(f"User Lookup: {'✅ Complete' if user_lookup_complete else '❌ Incomplete'}")
    
    # Failed days details
    if failed > 0:
        print(f"\n❌ Failed Days ({failed}):")
        for failure in progress.get('failed_days', []):
            date = failure.get('date', 'Unknown')
            error = failure.get('error', 'Unknown error')
            timestamp = failure.get('timestamp', 'Unknown time')
            print(f"   {date}: {error[:50]}{'...' if len(error) > 50 else ''}")
    
    # Recent completed days
    completed_days = progress.get('completed_days', [])
    if completed_days:
        recent_days = sorted(completed_days)[-5:]  # Last 5 completed
        print(f"\n✅ Recent Completed Days:")
        for day in recent_days:
            print(f"   {day}")

def reset_progress():
    """Reset all progress"""
    response = input("⚠️  This will reset ALL progress. Are you sure? (type 'RESET' to confirm): ")
    
    if response != 'RESET':
        print("Reset cancelled.")
        return
    
    progress_file = Path("analysis_progress.json")
    if progress_file.exists():
        progress_file.unlink()
        print("✅ Progress reset successfully.")
    else:
        print("ℹ️  No progress file found to reset.")

def reset_failed():
    """Reset only failed days so they can be retried"""
    progress = load_progress()
    
    if not progress:
        print("❌ No progress file found.")
        return
    
    failed_days = progress.get('failed_days', [])
    if not failed_days:
        print("ℹ️  No failed days to reset.")
        return
    
    print(f"Found {len(failed_days)} failed days:")
    for failure in failed_days:
        date = failure.get('date', 'Unknown')
        error = failure.get('error', 'Unknown error')
        print(f"   {date}: {error[:50]}{'...' if len(error) > 50 else ''}")
    
    response = input(f"\nReset {len(failed_days)} failed days for retry? (y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        progress['failed_days'] = []
        if save_progress(progress):
            print(f"✅ Reset {len(failed_days)} failed days. They will be retried on next run.")
        else:
            print("❌ Failed to save progress.")
    else:
        print("Reset cancelled.")

def remove_day():
    """Remove a specific day from completed list"""
    progress = load_progress()
    
    if not progress:
        print("❌ No progress file found.")
        return
    
    completed_days = progress.get('completed_days', [])
    if not completed_days:
        print("ℹ️  No completed days found.")
        return
    
    print("Completed days:")
    for i, day in enumerate(sorted(completed_days), 1):
        print(f"   {i}. {day}")
    
    try:
        choice = input("\nEnter day to remove (YYYY-MM-DD format) or number: ").strip()
        
        # Try as number first
        if choice.isdigit():
            idx = int(choice) - 1
            if 0 <= idx < len(completed_days):
                day_to_remove = sorted(completed_days)[idx]
            else:
                print("❌ Invalid number.")
                return
        else:
            # Try as date
            if choice in completed_days:
                day_to_remove = choice
            else:
                print("❌ Day not found in completed list.")
                return
        
        progress['completed_days'].remove(day_to_remove)
        if save_progress(progress):
            print(f"✅ Removed {day_to_remove} from completed list. It will be re-analyzed on next run.")
        else:
            print("❌ Failed to save progress.")
            
    except ValueError:
        print("❌ Invalid input.")

def main():
    parser = argparse.ArgumentParser(description="Manage Discord LLM Analysis Progress")
    parser.add_argument('action', choices=['status', 'reset', 'reset-failed', 'remove-day'], 
                       help='Action to perform')
    
    # If no arguments provided, show help
    if len(sys.argv) == 1:
        print("Discord LLM Analysis - Progress Manager")
        print("=" * 40)
        print("Usage:")
        print("  python manage_progress.py status       - Show current progress")
        print("  python manage_progress.py reset        - Reset all progress")
        print("  python manage_progress.py reset-failed - Reset only failed days")
        print("  python manage_progress.py remove-day   - Remove a specific completed day")
        print()
        print("Quick status check:")
        show_status()
        return
    
    args = parser.parse_args()
    
    if args.action == 'status':
        show_status()
    elif args.action == 'reset':
        reset_progress()
    elif args.action == 'reset-failed':
        reset_failed()
    elif args.action == 'remove-day':
        remove_day()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)
