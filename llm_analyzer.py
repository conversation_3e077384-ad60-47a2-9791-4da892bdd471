#!/usr/bin/env python3
"""
Discord Server Analysis Script using Local Ollama LLM

This script analyzes Discord messages from the archived database using a local Ollama LLM.
It creates user lookup tables, groups messages by day and channel, and generates analysis reports.
"""

import sqlite3
import json
import asyncio
import aiohttp
import logging
from datetime import datetime, date, timedelta
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import requests
import time
import re
from config import *

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

class DiscordLLMAnalyzer:
    def __init__(self, database_path: str = DATABASE_PATH, bot_token: str = BOT_TOKEN,
                 ollama_url: str = OLLAMA_URL):
        self.database_path = database_path
        self.bot_token = bot_token
        self.ollama_url = ollama_url
        self.model_name = MODEL_NAME
        self.max_context_tokens = MAX_CONTEXT_TOKENS
        self.user_lookup = {}
        self.channel_lookup = {}

        # Create output directory
        self.output_dir = Path(OUTPUT_DIR)
        self.output_dir.mkdir(exist_ok=True)
        
    async def create_user_lookup(self):
        """Create a lookup table for user IDs to usernames using Discord API"""
        logger.info("Creating user lookup table...")
        
        # Get all unique user IDs from database
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        cursor.execute("SELECT DISTINCT author_id FROM messages")
        user_ids = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        logger.info(f"Found {len(user_ids)} unique users")
        
        headers = {
            'Authorization': f'Bot {self.bot_token}',
            'Content-Type': 'application/json'
        }
        
        async with aiohttp.ClientSession(headers=headers) as session:
            for user_id in user_ids:
                try:
                    async with session.get(f'https://discord.com/api/v10/users/{user_id}') as response:
                        if response.status == 200:
                            user_data = await response.json()
                            username = user_data.get('username', f'Unknown_{user_id}')
                            display_name = user_data.get('global_name', username)
                            self.user_lookup[user_id] = {
                                'username': username,
                                'display_name': display_name
                            }
                            logger.debug(f"Found user: {username} ({user_id})")
                        else:
                            logger.warning(f"Failed to fetch user {user_id}: {response.status}")
                            self.user_lookup[user_id] = {
                                'username': f'Unknown_{user_id}',
                                'display_name': f'Unknown_{user_id}'
                            }
                    
                    # Rate limiting - Discord allows 50 requests per second
                    await asyncio.sleep(DISCORD_API_DELAY)
                    
                except Exception as e:
                    logger.error(f"Error fetching user {user_id}: {e}")
                    self.user_lookup[user_id] = {
                        'username': f'Unknown_{user_id}',
                        'display_name': f'Unknown_{user_id}'
                    }
        
        logger.info(f"Created lookup table for {len(self.user_lookup)} users")
        
        # Save user lookup to file for future use
        with open(self.output_dir / "user_lookup.json", 'w', encoding='utf-8') as f:
            json.dump(self.user_lookup, f, indent=2, ensure_ascii=False)
    
    def load_user_lookup(self):
        """Load existing user lookup table if available"""
        lookup_file = self.output_dir / "user_lookup.json"
        if lookup_file.exists():
            logger.info("Loading existing user lookup table...")
            with open(lookup_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # Convert string keys back to integers
                self.user_lookup = {int(k): v for k, v in data.items()}
            logger.info(f"Loaded lookup table for {len(self.user_lookup)} users")
            return True
        return False
    
    def get_messages_by_day(self) -> Dict[str, Dict[int, List[Dict]]]:
        """Group messages by day and channel"""
        logger.info("Grouping messages by day and channel...")
        
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        # Get all messages ordered by timestamp
        cursor.execute("""
            SELECT message_id, channel_id, author_id, message_timestamp, message_text
            FROM messages 
            ORDER BY message_timestamp
        """)
        
        messages_by_day = {}
        
        for row in cursor.fetchall():
            message_id, channel_id, author_id, timestamp, text = row
            
            # Convert timestamp to date
            msg_date = datetime.fromtimestamp(timestamp).date()
            date_str = msg_date.strftime("%Y-%m-%d")
            
            if date_str not in messages_by_day:
                messages_by_day[date_str] = {}
            
            if channel_id not in messages_by_day[date_str]:
                messages_by_day[date_str][channel_id] = []
            
            # Get username
            user_info = self.user_lookup.get(author_id, {
                'username': f'Unknown_{author_id}',
                'display_name': f'Unknown_{author_id}'
            })
            
            messages_by_day[date_str][channel_id].append({
                'message_id': message_id,
                'author_id': author_id,
                'username': user_info['username'],
                'display_name': user_info['display_name'],
                'timestamp': datetime.fromtimestamp(timestamp),
                'text': text or ''
            })
        
        conn.close()
        logger.info(f"Grouped messages into {len(messages_by_day)} days")
        return messages_by_day
    
    def format_messages_for_llm(self, date_str: str, messages_by_channel: Dict[int, List[Dict]]) -> str:
        """Format messages for LLM analysis"""
        prompt = f"""You are analyzing a chat log from a Discord server for Christian furries. The server is now deleted, but the owner would like to analyze and figure out where things went wrong and where I might have gone wrong and what I might do next time, as well as make sense of the anxiety and mental health problems given from the server. Focus especially on any interactions involving people with the usernames Guardian and YourLocalCowboy and any others that might cause stress in the future. If the conversation doesn't have a lot of content that might pertain to what the server owner wants, you can keep the summary short and simple. Otherwise, give any details needed to help the server owner.
These are the chat logs from {date_str}. I'd like you to analyze them and give a summary, including the participants, key events and disagreements, and emotional tone.

"""
        
        for channel_id, messages in messages_by_channel.items():
            if not messages:
                continue
                
            prompt += f"\nChannel ID: {channel_id}\n"
            
            for msg in messages:
                timestamp_str = msg['timestamp'].strftime("%H:%M:%S")
                username = msg['display_name'] or msg['username']
                text = msg['text'].strip()
                
                if text:  # Only include messages with content
                    prompt += f"[{timestamp_str}] {username}: {text}\n"
        
        return prompt
    
    def estimate_token_count(self, text: str) -> int:
        """Rough estimation of token count (approximately 4 characters per token)"""
        return len(text) // 4
    
    async def send_to_ollama(self, prompt: str) -> str:
        """Send prompt to Ollama and get response"""
        try:
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.ollama_url}/api/generate", json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get('response', 'No response received')
                    else:
                        error_text = await response.text()
                        logger.error(f"Ollama API error {response.status}: {error_text}")
                        return f"Error: Failed to get response from Ollama (status {response.status})"
        
        except Exception as e:
            logger.error(f"Error communicating with Ollama: {e}")
            return f"Error: {str(e)}"
    
    def split_messages_by_channel(self, messages_by_channel: Dict[int, List[Dict]]) -> List[Dict[int, List[Dict]]]:
        """Split messages into smaller chunks if they exceed context window"""
        chunks = []
        current_chunk = {}
        current_size = 0
        
        for channel_id, messages in messages_by_channel.items():
            channel_text = f"\nChannel ID: {channel_id}\n"
            for msg in messages:
                timestamp_str = msg['timestamp'].strftime("%H:%M:%S")
                username = msg['display_name'] or msg['username']
                text = msg['text'].strip()
                if text:
                    channel_text += f"[{timestamp_str}] {username}: {text}\n"
            
            estimated_tokens = self.estimate_token_count(channel_text)
            
            # If adding this channel would exceed limit, start new chunk
            if current_size + estimated_tokens > self.max_context_tokens * 0.8:  # Use 80% of limit for safety
                if current_chunk:
                    chunks.append(current_chunk)
                current_chunk = {channel_id: messages}
                current_size = estimated_tokens
            else:
                current_chunk[channel_id] = messages
                current_size += estimated_tokens
        
        # Add the last chunk
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks
    
    async def analyze_day(self, date_str: str, messages_by_channel: Dict[int, List[Dict]]):
        """Analyze messages for a single day"""
        logger.info(f"Analyzing messages for {date_str}")
        
        # Check if messages are too long and need splitting
        total_text = self.format_messages_for_llm(date_str, messages_by_channel)
        estimated_tokens = self.estimate_token_count(total_text)
        
        if estimated_tokens > self.max_context_tokens * 0.8:
            logger.info(f"Messages for {date_str} are too long ({estimated_tokens} tokens), splitting by channel")
            chunks = self.split_messages_by_channel(messages_by_channel)
            
            all_analyses = []
            for i, chunk in enumerate(chunks):
                chunk_prompt = self.format_messages_for_llm(f"{date_str} (Part {i+1}/{len(chunks)})", chunk)
                analysis = await self.send_to_ollama(chunk_prompt)
                all_analyses.append(f"=== Part {i+1}/{len(chunks)} ===\n{analysis}")
                
                # Small delay between requests
                await asyncio.sleep(OLLAMA_DELAY)
            
            final_analysis = "\n\n".join(all_analyses)
        else:
            final_analysis = await self.send_to_ollama(total_text)
        
        # Save analysis to file
        output_file = self.output_dir / f"analysis_{date_str}.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"Discord Server Analysis for {date_str}\n")
            f.write("=" * 50 + "\n\n")
            f.write(final_analysis)
        
        logger.info(f"Analysis for {date_str} saved to {output_file}")
    
    async def run_analysis(self):
        """Main analysis function"""
        logger.info("Starting Discord server analysis...")
        
        # Load or create user lookup table
        if not self.load_user_lookup():
            await self.create_user_lookup()
        
        # Get messages grouped by day
        messages_by_day = self.get_messages_by_day()
        
        # Analyze each day
        for date_str in sorted(messages_by_day.keys()):
            try:
                await self.analyze_day(date_str, messages_by_day[date_str])
                # Small delay between days to be respectful to the LLM
                await asyncio.sleep(DAY_ANALYSIS_DELAY)
            except Exception as e:
                logger.error(f"Error analyzing {date_str}: {e}")
                continue
        
        logger.info("Analysis complete!")

async def main():
    """Main entry point"""
    if not BOT_TOKEN or BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
        logger.error("Please set your Discord bot token in config.py")
        return

    analyzer = DiscordLLMAnalyzer()
    await analyzer.run_analysis()

if __name__ == "__main__":
    asyncio.run(main())
