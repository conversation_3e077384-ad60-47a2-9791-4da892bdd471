#!/usr/bin/env python3
"""
Discord Server Analysis Script using Local Ollama LLM

This script analyzes Discord messages from the archived database using a local Ollama LLM.
It creates user lookup tables, groups messages by day and channel, and generates analysis reports.
"""

import sqlite3
import json
import asyncio
import aiohttp
import logging
from datetime import datetime, date, timedelta
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import requests
import time
import re
from config import *

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

class DiscordLLMAnalyzer:
    def __init__(self, database_path: str = DATABASE_PATH, bot_token: str = BOT_TOKEN,
                 ollama_url: str = OLLAMA_URL):
        self.database_path = database_path
        self.bot_token = bot_token
        self.ollama_url = ollama_url
        self.model_name = MODEL_NAME
        self.max_context_tokens = MAX_CONTEXT_TOKENS
        self.user_lookup = {}
        self.channel_lookup = {}

        # Create output directory
        self.output_dir = Path(OUTPUT_DIR)
        self.output_dir.mkdir(exist_ok=True)

        # Progress tracking
        self.progress_file = Path(PROGRESS_FILE)
        self.progress_data = self.load_progress()

    def load_progress(self) -> dict:
        """Load analysis progress from file"""
        if not ENABLE_RESUME or not self.progress_file.exists():
            return {
                'user_lookup_complete': False,
                'completed_days': [],
                'failed_days': [],
                'last_updated': None,
                'total_days': 0,
                'current_day': None
            }

        try:
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                logger.info(f"Loaded progress: {len(data.get('completed_days', []))} days completed")
                return data
        except Exception as e:
            logger.warning(f"Could not load progress file: {e}")
            return {
                'user_lookup_complete': False,
                'completed_days': [],
                'failed_days': [],
                'last_updated': None,
                'total_days': 0,
                'current_day': None
            }

    def save_progress(self):
        """Save current progress to file"""
        try:
            self.progress_data['last_updated'] = datetime.now().isoformat()
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Could not save progress: {e}")

    def is_day_completed(self, date_str: str) -> bool:
        """Check if a day has already been analyzed"""
        return date_str in self.progress_data.get('completed_days', [])

    def mark_day_completed(self, date_str: str):
        """Mark a day as completed"""
        if date_str not in self.progress_data.get('completed_days', []):
            self.progress_data.setdefault('completed_days', []).append(date_str)
            self.save_progress()

    def mark_day_failed(self, date_str: str, error: str):
        """Mark a day as failed with error info"""
        failed_entry = {
            'date': date_str,
            'error': str(error),
            'timestamp': datetime.now().isoformat()
        }
        self.progress_data.setdefault('failed_days', []).append(failed_entry)
        self.save_progress()

    def get_progress_summary(self) -> dict:
        """Get a summary of analysis progress"""
        completed = len(self.progress_data.get('completed_days', []))
        failed = len(self.progress_data.get('failed_days', []))
        total = self.progress_data.get('total_days', 0)
        remaining = max(0, total - completed - failed)

        return {
            'completed': completed,
            'failed': failed,
            'remaining': remaining,
            'total': total,
            'completion_percentage': (completed / total * 100) if total > 0 else 0
        }

    async def create_user_lookup(self):
        """Create a lookup table for user IDs to usernames using Discord API"""
        logger.info("Creating user lookup table...")

        # Get all unique user IDs from database
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        cursor.execute("SELECT DISTINCT author_id FROM messages")
        user_ids = [row[0] for row in cursor.fetchall()]
        conn.close()

        # Filter out users we already have
        existing_users = set(self.user_lookup.keys())
        remaining_users = [uid for uid in user_ids if uid not in existing_users]

        logger.info(f"Found {len(user_ids)} unique users")
        if existing_users:
            logger.info(f"Already have {len(existing_users)} users, fetching {len(remaining_users)} remaining")

        if not remaining_users:
            logger.info("All users already in lookup table")
            return
        
        headers = {
            'Authorization': f'Bot {self.bot_token}',
            'Content-Type': 'application/json'
        }
        
        async with aiohttp.ClientSession(headers=headers) as session:
            for i, user_id in enumerate(remaining_users, 1):
                success = False
                retries = 0

                while not success and retries < DISCORD_API_MAX_RETRIES:
                    try:
                        async with session.get(f'https://discord.com/api/v10/users/{user_id}') as response:
                            if response.status == 200:
                                user_data = await response.json()
                                username = user_data.get('username', f'Unknown_{user_id}')
                                display_name = user_data.get('global_name', username)
                                self.user_lookup[user_id] = {
                                    'username': username,
                                    'display_name': display_name
                                }
                                logger.debug(f"Found user: {username} ({user_id})")
                                success = True

                            elif response.status == 429:
                                # Rate limited
                                retry_after = response.headers.get('Retry-After')
                                if retry_after:
                                    wait_time = int(retry_after)
                                else:
                                    wait_time = DISCORD_API_RETRY_DELAY

                                logger.warning(f"Rate limited on user {user_id}. Waiting {wait_time} seconds...")
                                await asyncio.sleep(wait_time)
                                retries += 1

                            elif response.status == 404:
                                # User not found (deleted account, etc.)
                                logger.debug(f"User {user_id} not found (404)")
                                self.user_lookup[user_id] = {
                                    'username': f'DeletedUser_{user_id}',
                                    'display_name': f'DeletedUser_{user_id}'
                                }
                                success = True

                            else:
                                logger.warning(f"Failed to fetch user {user_id}: {response.status}")
                                retries += 1
                                if retries < DISCORD_API_MAX_RETRIES:
                                    await asyncio.sleep(DISCORD_API_DELAY * retries)  # Exponential backoff

                    except Exception as e:
                        logger.error(f"Error fetching user {user_id}: {e}")
                        retries += 1
                        if retries < DISCORD_API_MAX_RETRIES:
                            await asyncio.sleep(DISCORD_API_DELAY * retries)

                # If all retries failed, mark as unknown
                if not success:
                    logger.error(f"Failed to fetch user {user_id} after {DISCORD_API_MAX_RETRIES} retries")
                    self.user_lookup[user_id] = {
                        'username': f'Unknown_{user_id}',
                        'display_name': f'Unknown_{user_id}'
                    }

                # Progress logging and batch saving
                if i % DISCORD_API_BATCH_SIZE == 0:
                    logger.info(f"User lookup progress: {i}/{len(remaining_users)} ({i/len(remaining_users)*100:.1f}%)")
                    # Save progress in batches
                    with open(self.output_dir / "user_lookup.json", 'w', encoding='utf-8') as f:
                        json.dump(self.user_lookup, f, indent=2, ensure_ascii=False)
                    logger.debug(f"Saved user lookup progress ({len(self.user_lookup)} users)")

                # Rate limiting between requests
                await asyncio.sleep(DISCORD_API_DELAY)
        
        logger.info(f"Created lookup table for {len(self.user_lookup)} users")

        # Save user lookup to file for future use
        with open(self.output_dir / "user_lookup.json", 'w', encoding='utf-8') as f:
            json.dump(self.user_lookup, f, indent=2, ensure_ascii=False)

        # Mark user lookup as complete
        self.progress_data['user_lookup_complete'] = True
        self.save_progress()
    
    def load_user_lookup(self):
        """Load existing user lookup table if available"""
        lookup_file = self.output_dir / "user_lookup.json"
        if lookup_file.exists():
            logger.info("Loading existing user lookup table...")
            with open(lookup_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # Convert string keys back to integers
                self.user_lookup = {int(k): v for k, v in data.items()}
            logger.info(f"Loaded lookup table for {len(self.user_lookup)} users")
            return True
        return False
    
    def get_messages_by_day(self) -> Dict[str, Dict[int, List[Dict]]]:
        """Group messages by day and channel"""
        logger.info("Grouping messages by day and channel...")
        
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        
        # Get all messages ordered by timestamp
        cursor.execute("""
            SELECT message_id, channel_id, author_id, message_timestamp, message_text
            FROM messages 
            ORDER BY message_timestamp
        """)
        
        messages_by_day = {}
        
        for row in cursor.fetchall():
            message_id, channel_id, author_id, timestamp, text = row
            
            # Convert timestamp to date
            msg_date = datetime.fromtimestamp(timestamp).date()
            date_str = msg_date.strftime("%Y-%m-%d")
            
            if date_str not in messages_by_day:
                messages_by_day[date_str] = {}
            
            if channel_id not in messages_by_day[date_str]:
                messages_by_day[date_str][channel_id] = []
            
            # Get username
            user_info = self.user_lookup.get(author_id, {
                'username': f'Unknown_{author_id}',
                'display_name': f'Unknown_{author_id}'
            })
            
            messages_by_day[date_str][channel_id].append({
                'message_id': message_id,
                'author_id': author_id,
                'username': user_info['username'],
                'display_name': user_info['display_name'],
                'timestamp': datetime.fromtimestamp(timestamp),
                'text': text or ''
            })
        
        conn.close()
        logger.info(f"Grouped messages into {len(messages_by_day)} days")
        return messages_by_day
    
    def format_messages_for_llm(self, date_str: str, messages_by_channel: Dict[int, List[Dict]]) -> str:
        """Format messages for LLM analysis"""
        prompt = f"""You are analyzing chat logs from a Discord server for Christian furries. The server is now deleted, but the owner, Hypercubed, would like to reflect on the past and learn from it.

Your task:
1. Summarize the activity in these logs in a way that is **as brief as possible unless there are meaningful events**. 
   - If the content is light, routine, or casual, provide only a short note (e.g. "mostly greetings and casual chatter").
   - If the content involves conflict, disagreements, moderation issues, or recurring tensions, give a more detailed summary.

2. Pay special attention to:
   - Interactions involving [G] and [C].
   - Situations that may have been stressful for Hypercubed, or that show signs of tension that could affect mental health.
   - Moments where Hypercubed directly participates in a way that might be important for reflection.

3. When relevant, highlight:
   - The participants and their dynamics.
   - Key events, disagreements, or conflicts.
   - The emotional tone of the discussion (e.g. supportive, tense, hostile, humorous).
   - Any possible lessons Hypercubed could take from that moment.

Format your output as:
- **Overall Summary**: A concise description of the day’s chat activity.
- **Notable Interactions** (only if applicable): Detailed breakdowns of specific conversations worth reflecting on.

If there is little or nothing meaningful, keep the output short and neutral.

Here are the chat logs from {date_str}:

"""
        
        for channel_id, messages in messages_by_channel.items():
            if not messages:
                continue
                
            prompt += f"\nChannel ID: {channel_id}\n"
            
            for msg in messages:
                timestamp_str = msg['timestamp'].strftime("%H:%M:%S")
                username = msg['display_name'] or msg['username']
                text = msg['text'].strip()
                
                if text:  # Only include messages with content
                    prompt += f"[{timestamp_str}] {username}: {text}\n"
        
        return prompt
    
    def estimate_token_count(self, text: str) -> int:
        """Rough estimation of token count (approximately 4 characters per token)"""
        return len(text) // 4
    
    async def send_to_ollama(self, prompt: str) -> str:
        """Send prompt to Ollama and get response"""
        try:
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.ollama_url}/api/generate", json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get('response', 'No response received')
                    else:
                        error_text = await response.text()
                        logger.error(f"Ollama API error {response.status}: {error_text}")
                        return f"Error: Failed to get response from Ollama (status {response.status})"
        
        except Exception as e:
            logger.error(f"Error communicating with Ollama: {e}")
            return f"Error: {str(e)}"
    
    def split_messages_by_channel(self, messages_by_channel: Dict[int, List[Dict]]) -> List[Dict[int, List[Dict]]]:
        """Split messages into smaller chunks if they exceed context window"""
        chunks = []
        current_chunk = {}
        current_size = 0
        
        for channel_id, messages in messages_by_channel.items():
            channel_text = f"\nChannel ID: {channel_id}\n"
            for msg in messages:
                timestamp_str = msg['timestamp'].strftime("%H:%M:%S")
                username = msg['display_name'] or msg['username']
                text = msg['text'].strip()
                if text:
                    channel_text += f"[{timestamp_str}] {username}: {text}\n"
            
            estimated_tokens = self.estimate_token_count(channel_text)
            
            # If adding this channel would exceed limit, start new chunk
            if current_size + estimated_tokens > self.max_context_tokens * 0.8:  # Use 80% of limit for safety
                if current_chunk:
                    chunks.append(current_chunk)
                current_chunk = {channel_id: messages}
                current_size = estimated_tokens
            else:
                current_chunk[channel_id] = messages
                current_size += estimated_tokens
        
        # Add the last chunk
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks
    
    async def analyze_day(self, date_str: str, messages_by_channel: Dict[int, List[Dict]]):
        """Analyze messages for a single day"""
        # Check if already completed
        if self.is_day_completed(date_str):
            logger.info(f"Skipping {date_str} - already completed")
            return

        # Update current day in progress
        self.progress_data['current_day'] = date_str
        self.save_progress()

        logger.info(f"Analyzing messages for {date_str}")

        # Check if messages are too long and need splitting
        total_text = self.format_messages_for_llm(date_str, messages_by_channel)
        estimated_tokens = self.estimate_token_count(total_text)
        
        if estimated_tokens > self.max_context_tokens * 0.8:
            logger.info(f"Messages for {date_str} are too long ({estimated_tokens} tokens), splitting by channel")
            chunks = self.split_messages_by_channel(messages_by_channel)
            
            all_analyses = []
            for i, chunk in enumerate(chunks):
                chunk_prompt = self.format_messages_for_llm(f"{date_str} (Part {i+1}/{len(chunks)})", chunk)
                analysis = await self.send_to_ollama(chunk_prompt)
                all_analyses.append(f"=== Part {i+1}/{len(chunks)} ===\n{analysis}")
                
                # Small delay between requests
                await asyncio.sleep(OLLAMA_DELAY)
            
            final_analysis = "\n\n".join(all_analyses)
        else:
            final_analysis = await self.send_to_ollama(total_text)
        
        # Save analysis to file
        output_file = self.output_dir / f"analysis_{date_str}.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"Discord Server Analysis for {date_str}\n")
            f.write("=" * 50 + "\n\n")
            f.write(final_analysis)

        # Mark day as completed
        self.mark_day_completed(date_str)
        logger.info(f"Analysis for {date_str} saved to {output_file}")
    
    async def run_analysis(self):
        """Main analysis function"""
        logger.info("Starting Discord server analysis...")

        # Load or create user lookup table
        if not self.progress_data.get('user_lookup_complete', False):
            if not self.load_user_lookup():
                logger.info("User lookup not found. Creating user lookup table...")
                logger.info("Note: If you encounter rate limiting issues, you can run 'python create_user_lookup.py' separately")
                try:
                    await self.create_user_lookup()
                except Exception as e:
                    logger.error(f"User lookup creation failed: {e}")
                    logger.info("You can run 'python create_user_lookup.py' separately to handle rate limits better")
                    raise
            else:
                self.progress_data['user_lookup_complete'] = True
                self.save_progress()
        else:
            logger.info("User lookup already complete, loading from cache...")
            self.load_user_lookup()

        # Get messages grouped by day
        messages_by_day = self.get_messages_by_day()

        # Update total days count
        self.progress_data['total_days'] = len(messages_by_day)
        self.save_progress()

        # Show progress summary
        progress = self.get_progress_summary()
        logger.info(f"Analysis Progress: {progress['completed']}/{progress['total']} days completed "
                   f"({progress['completion_percentage']:.1f}%)")

        if progress['completed'] > 0:
            logger.info(f"Resuming analysis from where we left off...")

        # Analyze each day
        sorted_dates = sorted(messages_by_day.keys())
        for i, date_str in enumerate(sorted_dates, 1):
            try:
                # Show progress
                logger.info(f"Processing day {i}/{len(sorted_dates)}: {date_str}")

                await self.analyze_day(date_str, messages_by_day[date_str])

                # Show updated progress
                progress = self.get_progress_summary()
                logger.info(f"Progress: {progress['completed']}/{progress['total']} days "
                           f"({progress['completion_percentage']:.1f}%)")

                # Small delay between days to be respectful to the LLM
                await asyncio.sleep(DAY_ANALYSIS_DELAY)

            except KeyboardInterrupt:
                logger.info(f"Analysis paused by user. Progress saved.")
                logger.info(f"To resume, run the script again.")
                raise
            except Exception as e:
                logger.error(f"Error analyzing {date_str}: {e}")
                self.mark_day_failed(date_str, str(e))
                continue

        # Final progress summary
        progress = self.get_progress_summary()
        logger.info("Analysis complete!")
        logger.info(f"Final results: {progress['completed']} days completed, "
                   f"{progress['failed']} days failed, {progress['total']} total days")

async def main():
    """Main entry point"""
    if not BOT_TOKEN or BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
        logger.error("Please set your Discord bot token in config.py")
        return

    analyzer = DiscordLLMAnalyzer()
    await analyzer.run_analysis()

if __name__ == "__main__":
    asyncio.run(main())
