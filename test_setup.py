#!/usr/bin/env python3
"""
Test script to verify the analysis setup is working correctly
"""

import asyncio
import sqlite3
import aiohttp
import json
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_database():
    """Test database connectivity and structure"""
    print("🔍 Testing database...")
    
    try:
        conn = sqlite3.connect("database/cfvrc.sqlite")
        cursor = conn.cursor()
        
        # Test basic query
        cursor.execute("SELECT COUNT(*) FROM messages")
        count = cursor.fetchone()[0]
        print(f"   ✅ Database connected: {count:,} messages found")
        
        # Test date range
        cursor.execute("SELECT MIN(datetime(message_timestamp, 'unixepoch')), MAX(datetime(message_timestamp, 'unixepoch')) FROM messages")
        min_date, max_date = cursor.fetchone()
        print(f"   ✅ Date range: {min_date} to {max_date}")
        
        # Test unique users
        cursor.execute("SELECT COUNT(DISTINCT author_id) FROM messages")
        users = cursor.fetchone()[0]
        print(f"   ✅ Unique users: {users}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Database error: {e}")
        return False

async def test_ollama():
    """Test Ollama connectivity"""
    print("🔍 Testing Ollama connection...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Test if Ollama is running
            async with session.get("http://localhost:11434/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    models = [model['name'] for model in data.get('models', [])]
                    print(f"   ✅ Ollama is running")
                    print(f"   📋 Available models: {', '.join(models)}")
                    
                    if 'deepseek-r1:8b' in models:
                        print("   ✅ deepseek-r1:8b model is available")
                        return True
                    else:
                        print("   ⚠️  deepseek-r1:8b model not found")
                        print("   💡 Run: ollama pull deepseek-r1:8b")
                        return False
                else:
                    print(f"   ❌ Ollama API error: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"   ❌ Ollama connection error: {e}")
        print("   💡 Make sure Ollama is running: ollama serve")
        return False

async def test_discord_api():
    """Test Discord API connectivity"""
    print("🔍 Testing Discord API...")
    
    try:
        from config import BOT_TOKEN
        
        if not BOT_TOKEN or BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
            print("   ❌ Bot token not configured")
            print("   💡 Set your bot token in config.py")
            return False
        
        headers = {
            'Authorization': f'Bot {BOT_TOKEN}',
            'Content-Type': 'application/json'
        }
        
        async with aiohttp.ClientSession(headers=headers) as session:
            # Test with a simple API call
            async with session.get('https://discord.com/api/v10/users/@me') as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ Discord API connected as: {data.get('username', 'Unknown')}")
                    return True
                else:
                    print(f"   ❌ Discord API error: {response.status}")
                    if response.status == 401:
                        print("   💡 Check your bot token in config.py")
                    return False
                    
    except ImportError:
        print("   ❌ config.py not found")
        return False
    except Exception as e:
        print(f"   ❌ Discord API error: {e}")
        return False

async def test_small_analysis():
    """Test a small analysis with a few messages"""
    print("🔍 Testing small analysis...")
    
    try:
        # Get a small sample of messages
        conn = sqlite3.connect("database/cfvrc.sqlite")
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT message_id, channel_id, author_id, message_timestamp, message_text
            FROM messages 
            WHERE message_text IS NOT NULL AND message_text != ''
            ORDER BY message_timestamp DESC
            LIMIT 5
        """)
        
        messages = cursor.fetchall()
        conn.close()
        
        if not messages:
            print("   ❌ No messages with text found")
            return False
        
        print(f"   ✅ Found {len(messages)} sample messages")
        
        # Test Ollama with a simple prompt
        test_prompt = "Analyze this sample: Hello, this is a test message."
        
        payload = {
            "model": "deepseek-r1:8b",
            "prompt": test_prompt,
            "stream": False,
            "options": {
                "temperature": 0.7
            }
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post("http://localhost:11434/api/generate", json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    analysis = result.get('response', '')
                    print(f"   ✅ LLM analysis test successful")
                    print(f"   📝 Sample response: {analysis[:100]}...")
                    return True
                else:
                    print(f"   ❌ LLM analysis failed: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"   ❌ Analysis test error: {e}")
        return False

async def main():
    """Run all tests"""
    print("Discord LLM Analyzer - Setup Test")
    print("=" * 40)
    
    tests = [
        ("Database", test_database),
        ("Ollama", test_ollama),
        ("Discord API", test_discord_api),
        ("Small Analysis", test_small_analysis)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{test_name} Test:")
        results[test_name] = await test_func()
    
    print("\n" + "=" * 40)
    print("Test Summary:")
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! You're ready to run the analysis.")
        print("   Run: python run_analysis.py")
        print("\n💡 Useful commands:")
        print("   python run_analysis.py           - Start/resume analysis")
        print("   python manage_progress.py        - Check progress status")
        print("   python manage_progress.py reset  - Reset all progress")
    else:
        print("\n⚠️  Some tests failed. Please fix the issues above before running the analysis.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    import sys
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)
