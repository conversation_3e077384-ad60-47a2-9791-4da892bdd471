import os
import shutil
from pathlib import Path

def copy_images_to_consolidated_folder():
    """
    Copy all images from channel_1080413573232214086 and any images starting with 'VRChat' 
    from other channel folders to a consolidated folder.
    """
    
    # Define paths
    images_dir = Path("images")
    target_channel = "channel_1080413573232214086"
    consolidated_dir = Path("vrchat_pics_consolidated")
    
    # Create consolidated directory
    consolidated_dir.mkdir(exist_ok=True)
    
    # Check if images directory exists
    if not images_dir.exists():
        print(f"Error: {images_dir} directory not found!")
        return
    
    copied_count = 0
    skipped_count = 0
    
    # Process all channel directories
    for channel_dir in images_dir.iterdir():
        if not channel_dir.is_dir():
            continue
            
        channel_name = channel_dir.name
        print(f"Processing {channel_name}...")
        
        # Get all image files in this channel directory
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg'}
        image_files = [f for f in channel_dir.iterdir() 
                      if f.is_file() and f.suffix.lower() in image_extensions]
        
        for image_file in image_files:
            should_copy = False
            
            # Copy all images from the target channel
            if channel_name == target_channel:
                should_copy = True
                print(f"  Found image from target channel: {image_file.name}")
            
            # Copy images that start with "VRChat" from any channel
            elif image_file.name.startswith("VRChat"):
                should_copy = True
                print(f"  Found VRChat image in {channel_name}: {image_file.name}")
            
            if should_copy:
                # Generate unique filename in destination
                dest_file = consolidated_dir / image_file.name
                counter = 1
                
                # Handle filename conflicts
                while dest_file.exists():
                    stem = image_file.stem
                    suffix = image_file.suffix
                    dest_file = consolidated_dir / f"{stem}_{counter}{suffix}"
                    counter += 1
                
                try:
                    shutil.copy2(image_file, dest_file)
                    copied_count += 1
                    print(f"    Copied to: {dest_file.name}")
                except Exception as e:
                    print(f"    Error copying {image_file.name}: {e}")
                    skipped_count += 1
    
    print(f"\n=== Consolidation Summary ===")
    print(f"Images copied: {copied_count}")
    print(f"Images skipped (errors): {skipped_count}")
    print(f"Consolidated folder: {consolidated_dir.absolute()}")

if __name__ == "__main__":
    copy_images_to_consolidated_folder()
